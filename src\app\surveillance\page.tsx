"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import FilterPanel from "@/components/FilterPanel";
import DynamicSurveillanceMap from "@/components/DynamicSurveillanceMap";
import SurveillanceFeed from "@/components/SurveillanceFeed";
import {
  SurveillanceActivity,
  SurveillanceFilters,
} from "@/types/surveillance";
import { surveillanceApi } from "@/services/api";

export default function SurveillancePage() {
  const [activities, setActivities] = useState<SurveillanceActivity[]>([]);
  const [containerData, setContainerData] = useState<ContainerData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filteredActivities, setFilteredActivities] = useState<
    SurveillanceActivity[]
  >([]);
  const [filters, setFilters] = useState<SurveillanceFilters>({
    date: new Date().toISOString().split("T")[0],
    townCode: undefined,
    ucCode: undefined,
    fieldWorker: undefined,
  });

  const fetchSurveillanceData = async (newFilters: SurveillanceFilters) => {
    setLoading(true);
    setError(null);

    try {
      const response = await surveillanceApi.getSurveillanceData(newFilters);
      setActivities(response.combined_data || []);
      setContainerData(response.container_data || []);
    } catch (err) {
      setError("Failed to fetch surveillance data. Please try again.");
      console.error("Error fetching surveillance data:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (newFilters: SurveillanceFilters) => {
    setFilters(newFilters);
    fetchSurveillanceData(newFilters);
  };

  // Filter activities based on field worker selection
  useEffect(() => {
    if (!filters.fieldWorker) {
      setFilteredActivities(activities);
    } else {
      setFilteredActivities(
        activities.filter(
          (activity) => activity.Submitted_by === filters.fieldWorker
        )
      );
    }
  }, [activities, filters.fieldWorker]);

  // Get unique field workers for dropdown
  const fieldWorkers = Array.from(
    new Set(activities.map((activity) => activity.Submitted_by))
  )
    .filter(Boolean)
    .sort();

  // Initial data fetch
  useEffect(() => {
    fetchSurveillanceData(filters);
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/" className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold">🦟</span>
                </div>
                <span className="text-xl font-semibold text-gray-900">DTS</span>
              </Link>
              <div className="h-6 w-px bg-gray-300"></div>
              <h1 className="text-2xl font-bold text-gray-900">
                Vector Surveillance
              </h1>
            </div>
            <nav className="hidden md:flex space-x-6">
              <Link
                href="/"
                className="text-gray-600 hover:text-blue-600 transition-colors"
              >
                Dashboard
              </Link>
              <Link href="/surveillance" className="text-blue-600 font-medium">
                Surveillance
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 py-6">
        {/* Filter Panel */}
        <div className="mb-6">
          <FilterPanel
            filters={filters}
            onFilterChange={handleFilterChange}
            loading={loading}
            fieldWorkers={fieldWorkers}
          />
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center">
              <div className="text-red-400 mr-3">
                <svg
                  className="w-5 h-5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div>
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <p className="text-sm text-red-700 mt-1">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-gray-600">
              Loading surveillance data...
            </span>
          </div>
        )}

        {/* Content */}
        {!loading && !error && (
          <>
            {/* Stats Summary */}
            <div className="grid grid-cols-1 md:grid-cols-1 gap-4 mb-6">
              <div className="bg-white rounded-lg shadow p-6">
                <div className="text-2xl font-bold text-blue-600">
                  {filteredActivities.length}
                </div>
                <div className="text-sm text-gray-600">Total Activities</div>
              </div>
            </div>

            {/* Map Component */}
            {filteredActivities.length > 0 && (
              <div className="mb-6">
                <DynamicSurveillanceMap activities={filteredActivities} />
              </div>
            )}

            {/* Feed Component */}
            <SurveillanceFeed
              activities={filteredActivities}
              containerData={containerData}
              loading={loading}
            />
          </>
        )}
      </main>
    </div>
  );
}
