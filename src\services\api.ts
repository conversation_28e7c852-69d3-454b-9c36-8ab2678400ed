import axios from "axios";
import {
  Town,
  UC,
  SurveillanceActivity,
  ContainerData,
  SurveillanceFilters,
} from "@/types/surveillance";

const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000/api/v1";

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Add request interceptor for debugging
api.interceptors.request.use(
  (config) => {
    console.log(
      "API Request:",
      config.method?.toUpperCase(),
      config.url,
      config.params
    );
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error("API Error Details:", {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message,
      url: error.config?.url,
      params: error.config?.params,
    });
    return Promise.reject(error);
  }
);

export const surveillanceApi = {
  // Get all towns
  getTowns: async (): Promise<Town[]> => {
    try {
      const response = await api.get("/towns");
      return response.data;
    } catch (error: any) {
      console.error("Error fetching towns:", {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
      });
      // Return empty array if API fails
      return [];
    }
  },

  // Get UCs for a specific town
  getUCs: async (townCode: number): Promise<UC[]> => {
    try {
      const response = await api.get(`/towns/${townCode}/ucs`);
      return response.data;
    } catch (error: any) {
      console.error("Error fetching UCs:", {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
      });
      // Return empty array if API fails
      return [];
    }
  },

  // Get surveillance data with filters
  getSurveillanceData: async (
    filters: SurveillanceFilters
  ): Promise<SurveillanceActivity[]> => {
    try {
      const params: any = {
        date: filters.date,
      };

      if (filters.townCode) {
        params.town_code = filters.townCode;
      }

      if (filters.ucCode) {
        params.uc_code = filters.ucCode;
      }

      console.log("Sending request with params:", params);

      const response = await api.get("/surveillance-data", { params });
      console.log("API Response:", response.data);
      return response.data;
    } catch (error: any) {
      console.error("Error fetching surveillance data:", {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
        params: error.config?.params,
      });

      // Return empty array instead of throwing to prevent app crash
      if (error.response?.status === 422) {
        console.warn("API returned 422 - returning empty array");
        return [];
      }

      throw error;
    }
  },

  // Get container data for a specific activity
  getContainerData: async (activityId: string): Promise<ContainerData[]> => {
    try {
      const response = await api.get(
        `/surveillance-data/${activityId}/containers`
      );
      return response.data;
    } catch (error) {
      console.error("Error fetching container data:", error);
      throw error;
    }
  },
};

export default api;
