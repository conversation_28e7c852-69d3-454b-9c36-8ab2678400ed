import axios from 'axios';
import { Town, UC, SurveillanceActivity, ContainerData, SurveillanceFilters } from '@/types/surveillance';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor for debugging
api.interceptors.request.use(
  (config) => {
    console.log('API Request:', config.method?.toUpperCase(), config.url, config.params);
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

export const surveillanceApi = {
  // Get all towns
  getTowns: async (): Promise<Town[]> => {
    try {
      const response = await api.get('/towns');
      return response.data;
    } catch (error) {
      console.error('Error fetching towns:', error);
      throw error;
    }
  },

  // Get UCs for a specific town
  getUCs: async (townCode: number): Promise<UC[]> => {
    try {
      const response = await api.get(`/towns/${townCode}/ucs`);
      return response.data;
    } catch (error) {
      console.error('Error fetching UCs:', error);
      throw error;
    }
  },

  // Get surveillance data with filters
  getSurveillanceData: async (filters: SurveillanceFilters): Promise<SurveillanceActivity[]> => {
    try {
      const params: any = {
        date: filters.date,
      };
      
      if (filters.townCode) {
        params.town_code = filters.townCode;
      }
      
      if (filters.ucCode) {
        params.uc_code = filters.ucCode;
      }

      const response = await api.get('/surveillance-data', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching surveillance data:', error);
      throw error;
    }
  },

  // Get container data for a specific activity
  getContainerData: async (activityId: string): Promise<ContainerData[]> => {
    try {
      const response = await api.get(`/surveillance-data/${activityId}/containers`);
      return response.data;
    } catch (error) {
      console.error('Error fetching container data:', error);
      throw error;
    }
  },
};

export default api;
