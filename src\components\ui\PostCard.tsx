"use client";

import { useState } from "react";
import { SurveillanceActivity, ContainerData } from "@/types/surveillance";

interface PostCardProps {
  activity: SurveillanceActivity;
  containerData?: ContainerData[];
}

export default function PostCard({
  activity,
  containerData = [],
}: PostCardProps) {
  const [imageError, setImageError] = useState(false);

  const formatDateTime = (dateTime: string) => {
    const date = new Date(dateTime);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
    };
  };

  const getTagColor = (tag: string) => {
    switch (tag.toLowerCase()) {
      case "house visit":
        return "bg-blue-100 text-blue-800";
      case "shop visit":
        return "bg-green-100 text-green-800";
      case "container check":
        return "bg-amber-100 text-amber-800";
      case "breeding site":
        return "bg-red-100 text-red-800";
      case "treatment":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((word) => word.charAt(0))
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const { date, time } = formatDateTime(activity.Activity_DateTime);

  return (
    <div className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 overflow-hidden">
      {/* Header */}
      <div className="p-6 pb-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-semibold text-lg shadow-md">
              {getInitials(activity.Name_of_Family_Head)}
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 text-lg">
                {activity.Name_of_Family_Head}
              </h3>
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <span>{date}</span>
                <span>•</span>
                <span>{time}</span>
                <span>•</span>
                <span className="text-blue-600">{activity.Submitted_by}</span>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <span
              className={`px-3 py-1 rounded-full text-xs font-medium ${getTagColor(
                activity.Tag
              )}`}
            >
              {activity.Tag}
            </span>
          </div>
        </div>

        {/* Activity Content */}
        <div className="space-y-3">
          <div className="flex items-start space-x-2">
            <span className="text-gray-400 mt-1">📍</span>
            <div className="text-gray-700">
              <div className="font-medium">{activity.Address}</div>
              <div className="text-sm text-gray-600">
                {activity.Locality}, {activity.Town}, {activity.UC}
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-4 text-sm text-gray-600">
            <div className="flex items-center space-x-1">
              <span>🏠</span>
              <span>{activity.Shop_House}</span>
            </div>
            <div className="flex items-center space-x-1">
              <span>🆔</span>
              <span>{activity.Activity_ID}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Image */}
      {activity.Picture && !imageError && (
        <div className="px-6 pb-4">
          <img
            src={activity.Picture}
            alt="Activity"
            className="w-full rounded-lg max-h-96 object-cover shadow-sm"
            onError={() => setImageError(true)}
          />
        </div>
      )}

      {/* Container Data Table */}
      {containerData && containerData.length > 0 && (
        <div className="px-6 pb-4">
          <h4 className="text-sm font-medium text-gray-900 mb-3">
            Container Data
          </h4>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 border border-gray-200 rounded-lg">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Container Type
                  </th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Water Source
                  </th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Larvae Found
                  </th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Action Taken
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {containerData.map((container, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-900">
                      {container.Container_type}
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-900">
                      {container.Water_source}
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm">
                      <span
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          container.Larvae_found === "Yes"
                            ? "bg-red-100 text-red-800"
                            : "bg-green-100 text-green-800"
                        }`}
                      >
                        {container.Larvae_found}
                      </span>
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-900">
                      {container.Action_taken}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Footer */}
      <div className="px-6 py-4 bg-gray-50 border-t border-gray-100">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-4 text-gray-600">
            <div className="flex items-center space-x-1">
              <span>📍</span>
              <span>Lat: {activity.Latitude.toFixed(6)}</span>
            </div>
            <div className="flex items-center space-x-1">
              <span>📍</span>
              <span>Lng: {activity.Longitude.toFixed(6)}</span>
            </div>
          </div>
          <div className="text-gray-500">#{activity.Sr_No}</div>
        </div>
      </div>
    </div>
  );
}
